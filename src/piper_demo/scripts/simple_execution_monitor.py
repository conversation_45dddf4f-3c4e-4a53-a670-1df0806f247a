#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单的MoveIt执行监控器
功能：
1. 检测关节运动并自动记录
2. 运动停止后自动生成图表
3. 解决中文乱码问题
"""

import rospy
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from sensor_msgs.msg import JointState
import math
import time
from datetime import datetime
import os

class SimpleExecutionMonitor:
    def __init__(self):
        """初始化简单执行监控器"""
        rospy.init_node('simple_execution_monitor', anonymous=True)
        
        # 设置matplotlib
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial']
        plt.rcParams['axes.unicode_minus'] = False
        plt.rcParams['figure.figsize'] = (12, 8)
        plt.rcParams['font.size'] = 11
        
        # 数据存储
        self.joint_data = {'Time': []}
        self.joint_names = ['Joint1', 'Joint2', 'Joint3', 'Joint4', 'Joint5', 'Joint6']
        for name in self.joint_names:
            self.joint_data[name] = []
        
        # 监控状态
        self.is_recording = False
        self.last_joint_positions = None
        self.motion_threshold = 0.01  # 运动检测阈值
        self.stationary_time = 0
        self.stationary_threshold = 2.0  # 静止2秒后停止记录
        self.last_motion_time = 0
        self.execution_count = 0
        self.start_time = None
        
        # 数据保存目录
        self.data_dir = os.path.expanduser("~/piper_trajectory_data")
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
        
        # 订阅关节状态
        self.joint_sub = rospy.Subscriber('/joint_states', JointState, self.joint_state_callback)
        
        print("=" * 60)
        print("🤖 Simple Execution Monitor Started")
        print("=" * 60)
        print("📋 Instructions:")
        print("1. Open MoveIt in RViz")
        print("2. Plan and Execute your trajectory")
        print("3. Monitor will detect motion automatically")
        print("4. Charts will be generated when motion stops")
        print("5. Press Ctrl+C to stop monitoring")
        print("-" * 60)
        print(f"📁 Data will be saved to: {self.data_dir}")
        print("=" * 60)
        
    def joint_state_callback(self, msg):
        """关节状态回调"""
        current_time = rospy.get_time()
        
        # 提取关节位置
        joint_names_ros = ['joint1', 'joint2', 'joint3', 'joint4', 'joint5', 'joint6']
        current_positions = []
        
        for joint_name in joint_names_ros:
            if joint_name in msg.name:
                idx = msg.name.index(joint_name)
                current_positions.append(msg.position[idx])
            else:
                current_positions.append(0.0)
        
        current_positions = np.array(current_positions)
        
        # 检测运动
        if self.last_joint_positions is not None:
            motion = np.linalg.norm(current_positions - self.last_joint_positions)
            
            if motion > self.motion_threshold:
                # 检测到运动
                if not self.is_recording:
                    self.start_recording()
                
                self.last_motion_time = current_time
                self.stationary_time = 0
                
                # 记录数据
                if self.is_recording:
                    relative_time = current_time - self.start_time
                    self.joint_data['Time'].append(relative_time)
                    
                    for i, name in enumerate(self.joint_names):
                        self.joint_data[name].append(current_positions[i])
                        
            else:
                # 没有运动
                if self.is_recording:
                    self.stationary_time = current_time - self.last_motion_time
                    
                    # 如果静止时间超过阈值，停止记录
                    if self.stationary_time > self.stationary_threshold:
                        self.stop_recording()
        
        self.last_joint_positions = current_positions.copy()
        
    def start_recording(self):
        """开始记录"""
        self.execution_count += 1
        self.is_recording = True
        self.start_time = rospy.get_time()
        
        # 清空数据
        self.joint_data = {'Time': []}
        for name in self.joint_names:
            self.joint_data[name] = []
            
        print(f"\n🚀 Motion detected! Recording execution #{self.execution_count}...")
        
    def stop_recording(self):
        """停止记录"""
        self.is_recording = False
        data_points = len(self.joint_data['Time'])
        
        print(f"✅ Motion stopped. Recorded {data_points} data points")
        
        if data_points > 10:  # 只有足够数据才生成图表
            self.generate_all_charts()
            print(f"📊 Charts generated for execution #{self.execution_count}")
        else:
            print("❌ Not enough data points to generate charts")
            
        print("-" * 60)
        print("Ready for next execution...")
        
    def generate_joint_angles_chart(self, timestamp):
        """生成关节角度图表"""
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('Piper Robot Joint Angles During Execution', fontsize=16, fontweight='bold')
        
        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#FFA07A', '#98D8C8', '#F7DC6F']
        time_data = self.joint_data['Time']
        
        for i, (joint_name, color) in enumerate(zip(self.joint_names, colors)):
            row, col = i // 3, i % 3
            ax = axes[row, col]
            
            angles_deg = np.degrees(self.joint_data[joint_name])
            ax.plot(time_data, angles_deg, color=color, linewidth=2.5)
            
            ax.set_title(f'{joint_name} Angle', fontsize=14, fontweight='bold')
            ax.set_xlabel('Time (s)', fontsize=12)
            ax.set_ylabel('Angle (degrees)', fontsize=12)
            ax.grid(True, alpha=0.3)
            
            # 统计信息
            min_angle = np.min(angles_deg)
            max_angle = np.max(angles_deg)
            range_angle = max_angle - min_angle
            stats_text = f'Range: {range_angle:.1f}°\nMin: {min_angle:.1f}°\nMax: {max_angle:.1f}°'
            ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, verticalalignment='top',
                   bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))
        
        plt.tight_layout()
        save_path = os.path.join(self.data_dir, f'joint_angles_exec{self.execution_count}_{timestamp}.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()
        return save_path
        
    def generate_velocity_chart(self, timestamp):
        """生成关节角速度图表"""
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('Joint Angular Velocities', fontsize=16, fontweight='bold')
        
        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#FFA07A', '#98D8C8', '#F7DC6F']
        time_data = np.array(self.joint_data['Time'])
        
        for i, (joint_name, color) in enumerate(zip(self.joint_names, colors)):
            row, col = i // 3, i % 3
            ax = axes[row, col]
            
            # 计算角速度
            angles = np.array(self.joint_data[joint_name])
            velocities = np.gradient(angles, time_data)
            velocities_deg = np.degrees(velocities)
            
            ax.plot(time_data, velocities_deg, color=color, linewidth=2.5)
            ax.set_title(f'{joint_name} Angular Velocity', fontsize=14, fontweight='bold')
            ax.set_xlabel('Time (s)', fontsize=12)
            ax.set_ylabel('Angular Velocity (deg/s)', fontsize=12)
            ax.grid(True, alpha=0.3)
            
            # 统计信息
            max_vel = np.max(np.abs(velocities_deg))
            rms_vel = np.sqrt(np.mean(velocities_deg**2))
            stats_text = f'Max: {max_vel:.1f}°/s\nRMS: {rms_vel:.1f}°/s'
            ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, verticalalignment='top',
                   bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))
        
        plt.tight_layout()
        save_path = os.path.join(self.data_dir, f'joint_velocities_exec{self.execution_count}_{timestamp}.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()
        return save_path
        
    def generate_3d_trajectory_chart(self, timestamp):
        """生成3D轨迹图表"""
        # 简化的正运动学计算
        x_data, y_data, z_data = [], [], []
        
        for i in range(len(self.joint_data['Time'])):
            q1 = self.joint_data['Joint1'][i]
            q2 = self.joint_data['Joint2'][i]
            q3 = self.joint_data['Joint3'][i]
            
            # 简化的DH参数计算
            L1, L2, L3 = 0.1273, 0.105, 0.098
            
            x = (L2 * math.cos(q2) + L3 * math.cos(q2 + q3)) * math.cos(q1)
            y = (L2 * math.cos(q2) + L3 * math.cos(q2 + q3)) * math.sin(q1)
            z = L1 + L2 * math.sin(q2) + L3 * math.sin(q2 + q3)
            
            x_data.append(x)
            y_data.append(y)
            z_data.append(z)
        
        if len(x_data) < 2:
            return None
            
        fig = plt.figure(figsize=(12, 10))
        ax = fig.add_subplot(111, projection='3d')
        
        ax.plot(x_data, y_data, z_data, 'b-', linewidth=3, label='Trajectory', alpha=0.8)
        ax.scatter(x_data[0], y_data[0], z_data[0], color='green', s=200, label='Start', marker='o')
        ax.scatter(x_data[-1], y_data[-1], z_data[-1], color='red', s=200, label='End', marker='s')
        
        ax.set_xlabel('X Position (m)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Y Position (m)', fontsize=12, fontweight='bold')
        ax.set_zlabel('Z Position (m)', fontsize=12, fontweight='bold')
        ax.set_title('End-Effector 3D Trajectory', fontsize=16, fontweight='bold')
        ax.legend(fontsize=12)
        
        save_path = os.path.join(self.data_dir, f'3d_trajectory_exec{self.execution_count}_{timestamp}.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()
        return save_path
        
    def save_data(self, timestamp):
        """保存数据到CSV"""
        joint_file = os.path.join(self.data_dir, f'execution_data_{self.execution_count}_{timestamp}.csv')
        with open(joint_file, 'w') as f:
            f.write('Time,Joint1,Joint2,Joint3,Joint4,Joint5,Joint6\n')
            for i in range(len(self.joint_data['Time'])):
                f.write(f"{self.joint_data['Time'][i]:.4f}")
                for name in self.joint_names:
                    f.write(f",{self.joint_data[name][i]:.6f}")
                f.write('\n')
        
        print(f"💾 Data saved: {os.path.basename(joint_file)}")
        
    def generate_all_charts(self):
        """生成所有图表"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存数据
        self.save_data(timestamp)
        
        print("📊 Generating charts...")
        saved_files = []
        
        try:
            # 1. 关节角度图表
            path1 = self.generate_joint_angles_chart(timestamp)
            if path1:
                saved_files.append(path1)
                print(f"✅ Joint angles chart: {os.path.basename(path1)}")
            
            # 2. 角速度图表
            path2 = self.generate_velocity_chart(timestamp)
            if path2:
                saved_files.append(path2)
                print(f"✅ Velocity chart: {os.path.basename(path2)}")
            
            # 3. 3D轨迹图表
            path3 = self.generate_3d_trajectory_chart(timestamp)
            if path3:
                saved_files.append(path3)
                print(f"✅ 3D trajectory chart: {os.path.basename(path3)}")
            
            print(f"🎉 Generated {len(saved_files)} charts for execution #{self.execution_count}")
            
        except Exception as e:
            print(f"❌ Error generating charts: {e}")
            import traceback
            traceback.print_exc()
            
    def run_monitor(self):
        """运行监控器"""
        try:
            rospy.spin()
        except KeyboardInterrupt:
            print("\n👋 Monitor stopped by user")
        except Exception as e:
            print(f"❌ Monitor error: {e}")

def main():
    """主函数"""
    try:
        monitor = SimpleExecutionMonitor()
        monitor.run_monitor()
    except KeyboardInterrupt:
        print("\n👋 Program interrupted by user")
    except Exception as e:
        print(f"❌ Program error: {e}")

if __name__ == '__main__':
    main()
